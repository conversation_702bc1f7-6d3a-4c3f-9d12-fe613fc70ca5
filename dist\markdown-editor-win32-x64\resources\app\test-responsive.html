<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Responsive Test - MarkDown Editor</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .test-container {
      max-width: 800px;
      margin: 0 auto;
    }
    .test-section {
      margin-bottom: 30px;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }
    .test-section h2 {
      margin-top: 0;
      color: #2c3e50;
    }
    .feature-list {
      list-style-type: none;
      padding: 0;
    }
    .feature-list li {
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }
    .feature-list li:before {
      content: "✓ ";
      color: #27ae60;
      font-weight: bold;
    }
    .instructions {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 5px;
      margin: 15px 0;
    }
    .keyboard-shortcut {
      background: #e9ecef;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: monospace;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>Responsive MarkDown Editor - Test Guide</h1>
    
    <div class="test-section">
      <h2>🔧 Features Implemented</h2>
      <ul class="feature-list">
        <li>Mobile hamburger menu toggle</li>
        <li>Two-row mobile layout: Style bar + Edit/Split/Preview buttons</li>
        <li>Expandable preview window on click</li>
        <li>Improved dropdown positioning for mobile</li>
        <li>Touch-friendly interface</li>
        <li>Keyboard shortcuts for preview expansion</li>
        <li>Simplified responsive design with two main breakpoints</li>
      </ul>
    </div>

    <div class="test-section">
      <h2>📱 Mobile Menu Testing</h2>
      <div class="instructions">
        <p><strong>To test the mobile menu:</strong></p>
        <ol>
          <li>Resize your browser window to less than 768px width</li>
          <li>You should see a hamburger menu (☰) button appear in the header</li>
          <li>Click the hamburger button to open/close the mobile menu</li>
          <li>The menu should slide down with all toolbar options</li>
          <li>Dropdowns should work properly in mobile view</li>
          <li>Click outside the menu or press <span class="keyboard-shortcut">Escape</span> to close</li>
        </ol>
      </div>
    </div>

    <div class="test-section">
      <h2>🖥️ Preview Expansion Testing</h2>
      <div class="instructions">
        <p><strong>To test preview expansion:</strong></p>
        <ol>
          <li>Switch to "Preview" mode using the view toggle buttons</li>
          <li>Click anywhere in the preview area (not on links or buttons)</li>
          <li>The preview should expand to fullscreen</li>
          <li>You should see an "Exit Fullscreen" button in the top-right</li>
          <li>Click the exit button or press <span class="keyboard-shortcut">Escape</span> to exit</li>
          <li>Try using <span class="keyboard-shortcut">F11</span> to toggle fullscreen when in preview mode</li>
        </ol>
      </div>
    </div>

    <div class="test-section">
      <h2>⌨️ Keyboard Shortcuts</h2>
      <div class="instructions">
        <p><strong>New keyboard shortcuts:</strong></p>
        <ul>
          <li><span class="keyboard-shortcut">F11</span> - Toggle preview fullscreen (when in preview mode)</li>
          <li><span class="keyboard-shortcut">Escape</span> - Exit preview fullscreen</li>
          <li><span class="keyboard-shortcut">Escape</span> - Close mobile menu</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h2>📏 Responsive Breakpoints</h2>
      <div class="instructions">
        <p><strong>Test at these screen sizes:</strong></p>
        <ul>
          <li><strong>Desktop:</strong> > 768px - Full toolbar layout</li>
          <li><strong>Mobile:</strong> ≤ 768px - Hamburger menu</li>
        </ul>
        <p><em>Note: Dynamic adjustments for intermediate screen sizes have been removed for a cleaner, more predictable responsive behavior.</em></p>
      </div>
    </div>

    <div class="test-section">
      <h2>🎯 Testing Checklist</h2>
      <div class="instructions">
        <p><strong>Verify these behaviors:</strong></p>
        <ul>
          <li>Clean transition between desktop and mobile layouts at 768px</li>
          <li>Hamburger menu appears on mobile (≤768px)</li>
          <li>Mobile menu displays two rows: Style bar (top) + Edit/Split/Preview (bottom)</li>
          <li>Two-row layout doesn't overlap with edit area</li>
          <li>Mobile menu opens/closes smoothly</li>
          <li>Dropdowns stay within viewport bounds</li>
          <li>Preview expands on click in preview mode</li>
          <li>Fullscreen preview has exit button</li>
          <li>Keyboard shortcuts work as expected</li>
          <li>Touch interactions work on mobile devices</li>
          <li>All buttons remain accessible and usable</li>
          <li>No intermediate layout adjustments or dynamic menu bar changes</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h2>🚀 Quick Start</h2>
      <div class="instructions">
        <p><strong>Ready to test?</strong></p>
        <p><a href="index.html" style="display: inline-block; background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Open MarkDown Editor</a></p>
      </div>
    </div>
  </div>
</body>
</html>
